package config

// 系统任务ID常量配置
// 这些ID对应数据库中的系统任务记录

const (
	// 用户任务相关
	TASK_DAILY_SIGNIN       = 1 // 今日已获赠30体力，系统每日免费积分刷新
	TASK_INVITE_FRIEND      = 2 // 邀请朋友安装体验+100体力
	TASK_SHARE_CASE         = 3 // 分享AI执行任务的案例并入选案例库+50体力
	TASK_FEEDBACK           = 4 // 反馈意见或发布您的需求，被标星+50体力
	TASK_MEMBERSHIP_MONTHLY = 5 // 会员每月+3000体力
	TASK_FIRST_LOGIN        = 6 // 首次登录奖励+200体力
)

// GetTaskName 根据任务ID获取任务名称（用于日志和调试）
func GetTaskName(taskID uint) string {
	taskNames := map[uint]string{
		TASK_DAILY_SIGNIN:       "每日签到",
		TASK_INVITE_FRIEND:      "邀请好友",
		TASK_SHARE_CASE:         "分享案例",
		TASK_FEEDBACK:           "反馈建议",
		TASK_MEMBERSHIP_MONTHLY: "会员月度积分",
		TASK_FIRST_LOGIN:        "首次登录",
	}

	if name, exists := taskNames[taskID]; exists {
		return name
	}
	return "未知任务"
}
